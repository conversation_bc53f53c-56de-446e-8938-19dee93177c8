# CRI-Dockerd 快照功能

这个文档描述了在 cri-dockerd 中新增的容器快照功能，允许在删除容器时自动创建快照，并在创建新容器时自动使用最新的快照。

## 功能概述

### 1. 容器删除时自动快照
当删除具有 `aocyun.snapshot=true` 标签的容器时，系统会自动将容器提交为新的镜像快照。

### 2. 容器创建时自动恢复
当创建容器时，如果指定了 `aocyun.workspace` 标签，系统会自动查找对应工作空间的最新快照镜像并使用。

## 标签说明

### `aocyun.snapshot`
- **值**: `"true"` 或 `"false"`
- **用途**: 标识容器在删除时是否需要创建快照
- **位置**: 应设置在Pod的metadata.labels中（会自动传递到sandbox容器）
- **示例**: `aocyun.snapshot=true`

### `aocyun.workspace`
- **值**: 工作空间名称（字符串）
- **用途**: 标识容器所属的工作空间，用于快照的命名和查找
- **位置**: 应设置在Pod的metadata.labels中（会自动传递到sandbox容器）
- **示例**: `aocyun.workspace=jinxq-test-123123`

## 重要说明

**标签位置**: 在Kubernetes中，这些标签应该设置在Pod的`metadata.labels`中，而不是容器的labels中。cri-dockerd会自动从Pod的sandbox容器（pause容器）中读取这些标签。

## 快照镜像命名规则

快照镜像的命名格式为：
```
{原始镜像名}:{工作空间名}_{时间戳}
```

例如：
- 原始镜像: `nginx:v1`
- 工作空间: `jinxq-test-123123`
- 时间戳: `20210507090909`
- 快照镜像: `nginx:jinxq-test-123123_20210507090909`

## 使用示例

### 示例 1: 创建带快照功能的容器

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  labels:
    aocyun.snapshot: "true"
    aocyun.workspace: "jinxq-test-123123"
spec:
  containers:
  - name: nginx
    image: nginx:v1
    # ... 其他配置
```

### 示例 2: 删除容器时自动创建快照

当删除上述 Pod 时，系统会：
1. 检查容器是否有 `aocyun.snapshot=true` 标签
2. 获取 `aocyun.workspace` 标签的值
3. 使用 `docker commit` 创建快照镜像
4. 快照镜像名为: `nginx:jinxq-test-123123_20210507090909`

### 示例 3: 创建容器时自动使用快照

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: restored-pod
  labels:
    aocyun.workspace: "jinxq-test-123123"
spec:
  containers:
  - name: nginx
    image: nginx:v1  # 系统会自动替换为最新的快照镜像
    # ... 其他配置
```

系统会：
1. 检查是否有 `aocyun.workspace` 标签
2. 查找匹配工作空间的快照镜像
3. 如果找到，使用最新的快照镜像（如 `nginx:jinxq-test-123123_20210507090909`）
4. 如果没找到，使用原始镜像（`nginx:v1`）

## 工作流程

### 容器删除流程
1. 接收删除容器请求
2. 检查容器标签和对应sandbox（Pod）标签中是否有 `aocyun.snapshot=true`
3. 如果有，从容器和sandbox标签中获取 `aocyun.workspace` 标签值
4. 如果工作空间不为空，调用 Docker commit API 创建快照
5. 快照镜像名包含工作空间和当前时间戳
6. 继续正常的容器删除流程

### 容器创建流程
1. 接收创建容器请求
2. 合并容器标签和sandbox（Pod）标签，检查是否有 `aocyun.workspace`
3. 如果有，查找匹配工作空间的快照镜像
4. 按时间戳排序，选择最新的快照镜像
5. 如果找到快照镜像，替换原始镜像
6. 如果没找到，使用原始镜像
7. 继续正常的容器创建流程

## 注意事项

1. **快照功能仅在 Linux 平台支持**
2. **需要同时设置两个标签**: 删除时需要 `aocyun.snapshot=true` 和 `aocyun.workspace`
3. **时间戳格式**: 使用 `YYYYMMDDHHMMSS` 格式（如 `20210507090909`）
4. **镜像存储**: 快照镜像存储在本地 Docker 镜像仓库中
5. **权限要求**: 需要 Docker daemon 的 commit 权限

## 日志示例

### 创建快照时的日志
```
INFO Creating snapshot image nginx:jinxq-test-123123_20210507090909 from container abc123
INFO Successfully created snapshot image nginx:jinxq-test-123123_20210507090909 (ID: sha256:def456)
```

### 使用快照时的日志
```
INFO Found 2 snapshot images for nginx with workspace jinxq-test-123123, using latest: nginx:jinxq-test-123123_20210507090909
INFO Using snapshot image nginx:jinxq-test-123123_20210507090909 instead of nginx:v1 for workspace jinxq-test-123123
```

### 无快照时的日志
```
INFO No snapshot image found for nginx:v1 with workspace test-workspace, using original image
```

## 错误处理

- 如果容器不存在，跳过快照创建
- 如果有 `aocyun.snapshot=true` 但没有 `aocyun.workspace`，记录警告并跳过
- 如果 Docker commit 失败，返回错误并停止删除流程
- 如果查找快照镜像失败，记录警告并使用原始镜像

## 故障排除

### 问题1: 日志显示创建快照但 `docker images` 中没有镜像

**可能原因:**
1. **镜像名格式问题**: 原始镜像使用digest格式（如 `nginx@sha256:...` 或纯digest `sha256:...`）
2. **Docker commit权限问题**: cri-dockerd没有足够权限执行commit操作
3. **容器状态问题**: 容器在commit时状态不正确

**已修复的问题:**
- ✅ **纯digest格式处理**: 当容器镜像是纯digest格式（如 `sha256:abc123...`）时，系统会自动使用通用名称（如 `snapshot-workspace:workspace_timestamp`）来避免Docker的"refusing to create an ambiguous tag using digest algorithm as name"错误

**解决方案:**
1. 检查日志中的详细信息：
   ```
   INFO Parsed original image nginx@sha256:abc123 -> baseName: nginx, originalTag: latest
   INFO Creating snapshot image nginx:workspace_timestamp from container abc123
   INFO Container abc123 state before commit: Running=true, Paused=false, Status=running
   ```

2. 验证Docker权限：
   ```bash
   # 测试cri-dockerd用户是否能执行commit
   docker commit <container_id> test:snapshot
   ```

3. 检查容器状态：
   - 容器必须存在且可访问
   - 容器不能处于异常状态

### 问题2: commit操作失败

**常见错误信息:**
- `Error response from daemon: No such container`
- `Error response from daemon: cannot commit a paused container`
- `permission denied`

**解决方案:**
1. 确保容器在commit前仍然存在
2. 检查容器状态是否适合commit
3. 验证Docker daemon权限配置

### 问题3: 快照镜像创建成功但无法找到

**检查步骤:**
1. 使用 `docker images` 命令手动验证
2. 检查镜像名是否包含特殊字符
3. 验证镜像标签格式是否正确

**调试命令:**
```bash
# 列出所有镜像，包括中间镜像
docker images -a

# 搜索特定的快照镜像
docker images | grep workspace_

# 检查镜像历史
docker history <image_name>
```

## 测试

项目包含完整的单元测试和集成测试：
- `core/snapshot_helper_test.go`: 辅助函数测试
- `core/snapshot_integration_test.go`: 集成测试

运行测试：
```bash
go test ./core -v -run TestSnapshot
```
