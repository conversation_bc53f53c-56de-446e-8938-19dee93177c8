/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

/*
快照功能模块 (Snapshot Feature Module)

本模块实现了Pod生命周期管理中的快照功能，主要包括：
1. 容器删除时自动创建快照镜像
2. 容器创建时自动使用最新的快照镜像
3. 快照镜像的命名、查找和管理

核心设计理念：
- 简单的命名规则：workspace:timestamp
- 自动化的生命周期管理
- 向后兼容的镜像选择逻辑
*/

package core

import (
	"fmt"
	"sort"
	"strings"
	"time"

	dockercontainer "github.com/docker/docker/api/types/container"
	dockerimagetypes "github.com/docker/docker/api/types/image"
	"github.com/sirupsen/logrus"
)

const (
	// SnapshotLabel 快照功能启用标签键
	// 当Pod的labels中包含 "aocyun.snapshot=true" 时，容器删除时会自动创建快照
	SnapshotLabel = "aocyun.snapshot"

	// WorkspaceLabel 工作空间标识标签键
	// 用于标识Pod所属的工作空间，快照镜像会以此命名
	// 格式：aocyun.workspace=workspace-name
	WorkspaceLabel = "aocyun.workspace"

	// SnapshotTimeFormat 快照镜像时间戳格式
	// 使用Go的标准时间格式，生成如：20060102150405 的时间戳
	SnapshotTimeFormat = "20060102150405"
)

// SnapshotInfo 快照镜像信息结构体
// 用于存储和管理快照镜像的相关信息
type SnapshotInfo struct {
	ImageName string // 镜像名称（即工作空间名称）
	Workspace string // 工作空间名称
	Timestamp string // 快照创建时间戳
	FullTag   string // 完整的镜像标签（格式：workspace:timestamp）
}

// parseImageName 解析镜像名称，提取基础镜像名和标签
// 支持多种镜像引用格式：标签格式(nginx:v1)和摘要格式(nginx@sha256:...)
//
// 参数：
//   - imageName: 完整的镜像引用名称
//
// 返回值：
//   - string: 基础镜像名称
//   - string: 镜像标签
func parseImageName(imageName string) (string, string) {
	// 处理摘要格式 (nginx@sha256:...)
	if strings.Contains(imageName, "@") {
		parts := strings.Split(imageName, "@")
		return parts[0], "latest" // 摘要格式使用latest作为默认标签
	}

	// 处理纯摘要格式 (sha256:abc123...)
	// 这种格式通常出现在容器检查时，Docker返回的是纯摘要ID
	if strings.HasPrefix(imageName, "sha256:") || strings.HasPrefix(imageName, "sha1:") || strings.HasPrefix(imageName, "md5:") {
		// 这是纯摘要，返回unknown避免摘要算法名称问题
		// Docker会拒绝创建以摘要算法名称作为镜像名的标签
		return "unknown", "latest"
	}

	// 处理标签格式 (nginx:v1)
	parts := strings.Split(imageName, ":")
	if len(parts) == 1 {
		return imageName, "latest" // 没有标签时使用latest
	}
	// 支持包含端口号的registry地址 (registry.com:5000/nginx:v1)
	return strings.Join(parts[:len(parts)-1], ":"), parts[len(parts)-1]
}

// These functions are no longer needed with the new simple naming format
// New format: workspace:timestamp

// 旧的findLatestSnapshotImage函数已被findLatestSnapshotImageByWorkspace替代

// findLatestSnapshotImageByWorkspace 根据工作空间查找最新的快照镜像
// 使用新的命名格式：workspace:timestamp
//
// 功能说明：
// 1. 列出Docker中的所有镜像
// 2. 筛选出属于指定工作空间的快照镜像
// 3. 按时间戳排序，返回最新的快照镜像
//
// 参数：
//   - workspace: 工作空间名称
//
// 返回值：
//   - string: 最新快照镜像的完整标签，如果没有找到则返回空字符串
//   - error: 错误信息
func (ds *dockerService) findLatestSnapshotImageByWorkspace(workspace string) (string, error) {
	if workspace == "" {
		return "", nil
	}

	// 列出Docker中的所有镜像
	images, err := ds.client.ListImages(dockerimagetypes.ListOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to list images: %v", err)
	}

	var snapshots []SnapshotInfo

	// 查找属于此工作空间的所有快照镜像
	// 新的命名格式：workspace:timestamp
	for _, image := range images {
		for _, repoTag := range image.RepoTags {
			// 跳过无效的镜像标签
			if repoTag == "<none>:<none>" {
				continue
			}

			imageName, tag := parseImageName(repoTag)

			// 检查镜像名是否匹配工作空间名称
			if imageName != workspace {
				continue
			}

			// 验证时间戳格式长度
			if len(tag) != len(SnapshotTimeFormat) {
				continue
			}

			// 尝试解析时间戳以验证其有效性
			if _, err := time.Parse(SnapshotTimeFormat, tag); err != nil {
				continue
			}

			// 添加到快照列表
			snapshots = append(snapshots, SnapshotInfo{
				ImageName: imageName,
				Workspace: workspace,
				Timestamp: tag,
				FullTag:   repoTag,
			})
		}
	}

	// 如果没有找到快照镜像，返回空字符串
	if len(snapshots) == 0 {
		return "", nil
	}

	// 按时间戳排序（最新的在前）
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].Timestamp > snapshots[j].Timestamp
	})

	logrus.Infof("Found %d snapshot images for workspace %s, using latest: %s",
		len(snapshots), workspace, snapshots[0].FullTag)

	return snapshots[0].FullTag, nil
}

// shouldCreateSnapshot 检查容器是否应该创建快照
// 基于容器的标签判断是否启用快照功能
//
// 判断逻辑：
// 1. 检查labels中是否存在SnapshotLabel键
// 2. 检查该键的值是否为"true"（区分大小写）
//
// 参数：
//   - labels: 容器的标签映射
//
// 返回值：
//   - bool: true表示应该创建快照，false表示不应该创建
func shouldCreateSnapshot(labels map[string]string) bool {
	if labels == nil {
		return false
	}

	snapshotValue, exists := labels[SnapshotLabel]
	return exists && snapshotValue == "true"
}

// getWorkspaceFromLabels 从容器标签中提取工作空间值
// 工作空间用于快照镜像的命名和分组管理
//
// 参数：
//   - labels: 容器的标签映射
//
// 返回值：
//   - string: 工作空间名称，如果不存在则返回空字符串
func getWorkspaceFromLabels(labels map[string]string) string {
	if labels == nil {
		return ""
	}

	return labels[WorkspaceLabel]
}

// createSnapshotImage 提交容器创建快照镜像
// 这是快照功能的核心函数，负责将容器的当前状态保存为新的镜像
//
// 功能说明：
// 1. 验证工作空间参数的有效性
// 2. 使用简单的命名格式生成快照镜像名：workspace:timestamp
// 3. 调用Docker API提交容器为新镜像
// 4. 验证快照镜像是否成功创建
//
// 命名规则：
// - 格式：{workspace}:{timestamp}
// - 示例：jinxq-test-123123:20210507090909
// - 优势：简单、避免命名冲突、易于管理
//
// 参数：
//   - containerID: 要创建快照的容器ID
//   - originalImage: 容器的原始镜像名称（用于日志记录）
//   - workspace: 工作空间名称
//
// 返回值：
//   - error: 错误信息，成功时为nil
func (ds *dockerService) createSnapshotImage(containerID string, originalImage string, workspace string) error {
	if workspace == "" {
		return fmt.Errorf("workspace is required for creating snapshot")
	}

	// 使用新的简单格式生成快照镜像名：workspace:timestamp
	timestamp := time.Now().Format(SnapshotTimeFormat)
	snapshotImageName := fmt.Sprintf("%s:%s", workspace, timestamp)

	logrus.Infof("Creating snapshot image %s from container %s (original image: %s)", snapshotImageName, containerID, originalImage)

	// 配置Docker提交选项
	commitOpts := dockercontainer.CommitOptions{
		Reference: snapshotImageName,                                           // 新镜像的名称和标签
		Comment:   fmt.Sprintf("Snapshot created for workspace %s", workspace), // 提交注释
		Author:    "cri-dockerd-snapshot",                                      // 作者信息
		Pause:     true,                                                        // 提交时暂停容器以确保数据一致性
	}

	logrus.Infof("Creating snapshot image %s from container %s (original image: %s)", snapshotImageName, containerID, originalImage)
	logrus.Infof("Commit options: Reference=%s, Comment=%s, Author=%s, Pause=%t",
		commitOpts.Reference, commitOpts.Comment, commitOpts.Author, commitOpts.Pause)

	// 首先验证容器是否存在且处于可提交状态
	// 这一步很重要，可以提前发现问题并提供详细的错误信息
	containerInfo, inspectErr := ds.client.InspectContainer(containerID)
	if inspectErr != nil {
		logrus.Errorf("Cannot inspect container %s before commit: %v", containerID, inspectErr)
		return fmt.Errorf("failed to inspect container %s before commit: %v", containerID, inspectErr)
	}

	logrus.Infof("Container %s state before commit: Running=%t, Paused=%t, Status=%s",
		containerID, containerInfo.State.Running, containerInfo.State.Paused, containerInfo.State.Status)

	// 执行Docker提交操作
	response, err := ds.client.CommitContainer(containerID, commitOpts)
	if err != nil {
		logrus.Errorf("Failed to commit container %s to image %s: %v", containerID, snapshotImageName, err)
		logrus.Errorf("Container state during commit failure: Running=%t, Paused=%t, Status=%s",
			containerInfo.State.Running, containerInfo.State.Paused, containerInfo.State.Status)
		return fmt.Errorf("failed to commit container %s: %v", containerID, err)
	}

	logrus.Infof("Successfully created snapshot image %s (ID: %s)", snapshotImageName, response.ID)

	// 验证镜像是否成功创建
	// 通过列出所有镜像并查找新创建的快照镜像来确认
	images, listErr := ds.client.ListImages(dockerimagetypes.ListOptions{})
	if listErr == nil {
		found := false
		for _, img := range images {
			for _, tag := range img.RepoTags {
				if tag == snapshotImageName {
					found = true
					logrus.Infof("Verified snapshot image %s exists in Docker images list", snapshotImageName)
					break
				}
			}
			if found {
				break
			}
		}
		if !found {
			logrus.Warnf("Snapshot image %s not found in Docker images list after commit", snapshotImageName)
		}
	} else {
		logrus.Warnf("Failed to list images for verification: %v", listErr)
	}

	return nil
}

// selectImageForCreation 确定容器创建时使用的镜像
// 这是快照功能的另一个核心函数，负责在容器创建时智能选择镜像
//
// 功能说明：
// 1. 从容器标签中提取工作空间信息
// 2. 如果没有工作空间，直接使用原始镜像
// 3. 如果有工作空间，查找该工作空间的最新快照镜像
// 4. 如果找到快照镜像，优先使用快照镜像
// 5. 如果没有找到快照镜像，回退到原始镜像
//
// 这种设计确保了：
// - 向后兼容：没有快照时正常使用原始镜像
// - 自动化：有快照时自动使用最新快照
// - 容错性：查找失败时回退到原始镜像
//
// 参数：
//   - originalImage: 原始镜像名称
//   - labels: 容器标签（包含工作空间信息）
//
// 返回值：
//   - string: 最终选择的镜像名称
//   - error: 错误信息（通常为nil，因为有回退机制）
func (ds *dockerService) selectImageForCreation(originalImage string, labels map[string]string) (string, error) {
	workspace := getWorkspaceFromLabels(labels)
	if workspace == "" {
		// 没有指定工作空间，使用原始镜像
		return originalImage, nil
	}

	// 尝试为此工作空间查找最新的快照镜像
	// 使用新的命名格式：workspace:timestamp
	snapshotImage, err := ds.findLatestSnapshotImageByWorkspace(workspace)
	if err != nil {
		logrus.Warnf("Failed to find snapshot image for workspace %s: %v", workspace, err)
		return originalImage, nil
	}

	if snapshotImage == "" {
		// 没有找到快照，使用原始镜像
		logrus.Infof("No snapshot image found for workspace %s, using original image %s", workspace, originalImage)
		return originalImage, nil
	}

	// 使用快照镜像
	logrus.Infof("Using snapshot image %s instead of %s for workspace %s", snapshotImage, originalImage, workspace)
	return snapshotImage, nil
}
