/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package core

import (
	"fmt"
	"sort"
	"strings"
	"time"

	dockercontainer "github.com/docker/docker/api/types/container"
	dockerimagetypes "github.com/docker/docker/api/types/image"
	"github.com/sirupsen/logrus"
)

const (
	// Labels for snapshot functionality
	SnapshotLabel  = "aocyun.snapshot"
	WorkspaceLabel = "aocyun.workspace"

	// Time format for snapshot timestamps
	SnapshotTimeFormat = "20060102150405"
)

// SnapshotInfo contains information about a snapshot image
type SnapshotInfo struct {
	ImageName string
	Workspace string
	Timestamp string
	FullTag   string
}

// parseImageName extracts the base image name and tag from a full image reference
// Handles both tag format (nginx:v1) and digest format (nginx@sha256:...)
func parseImageName(imageName string) (string, string) {
	// Handle digest format (nginx@sha256:...)
	if strings.Contains(imageName, "@") {
		parts := strings.Split(imageName, "@")
		return parts[0], "latest" // Use latest as default tag for digest-based images
	}

	// Handle pure digest format (sha256:abc123...)
	if strings.HasPrefix(imageName, "sha256:") || strings.HasPrefix(imageName, "sha1:") || strings.HasPrefix(imageName, "md5:") {
		// This is a pure digest, we need to get the actual image name from container inspection
		// For now, return a generic name to avoid the digest algorithm issue
		return "unknown", "latest"
	}

	// Handle tag format (nginx:v1)
	parts := strings.Split(imageName, ":")
	if len(parts) == 1 {
		return imageName, "latest"
	}
	return strings.Join(parts[:len(parts)-1], ":"), parts[len(parts)-1]
}

// These functions are no longer needed with the new simple naming format
// New format: workspace:timestamp

// findLatestSnapshotImage finds the latest snapshot image for a given base image and workspace
func (ds *dockerService) findLatestSnapshotImage(baseImageName, workspace string) (string, error) {
	if workspace == "" {
		return "", nil
	}

	// List all images
	images, err := ds.client.ListImages(dockerimagetypes.ListOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to list images: %v", err)
	}

	var snapshots []SnapshotInfo

	// Find all snapshot images for this workspace
	// New format: workspace:timestamp
	for _, image := range images {
		for _, repoTag := range image.RepoTags {
			if repoTag == "<none>:<none>" {
				continue
			}

			imageName, tag := parseImageName(repoTag)

			// Check if this is a snapshot image for our workspace
			if imageName != workspace {
				continue
			}

			// Validate timestamp format
			if len(tag) != len(SnapshotTimeFormat) {
				continue
			}

			// Try to parse the timestamp to validate it
			if _, err := time.Parse(SnapshotTimeFormat, tag); err != nil {
				continue
			}

			snapshots = append(snapshots, SnapshotInfo{
				ImageName: imageName,
				Workspace: workspace,
				Timestamp: tag,
				FullTag:   repoTag,
			})
		}
	}

	if len(snapshots) == 0 {
		return "", nil
	}

	// Sort by timestamp (newest first)
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].Timestamp > snapshots[j].Timestamp
	})

	logrus.Infof("Found %d snapshot images for workspace %s, using latest: %s",
		len(snapshots), workspace, snapshots[0].FullTag)

	return snapshots[0].FullTag, nil
}

// shouldCreateSnapshot checks if a container should be snapshotted based on its labels
func shouldCreateSnapshot(labels map[string]string) bool {
	if labels == nil {
		return false
	}

	snapshotValue, exists := labels[SnapshotLabel]
	return exists && snapshotValue == "true"
}

// getWorkspaceFromLabels extracts the workspace value from container labels
func getWorkspaceFromLabels(labels map[string]string) string {
	if labels == nil {
		return ""
	}

	return labels[WorkspaceLabel]
}

// createSnapshotImage commits a container to create a snapshot image
func (ds *dockerService) createSnapshotImage(containerID string, originalImage string, workspace string) error {
	if workspace == "" {
		return fmt.Errorf("workspace is required for creating snapshot")
	}

	// Generate snapshot image name using new simple format: workspace:timestamp
	timestamp := time.Now().Format(SnapshotTimeFormat)
	snapshotImageName := fmt.Sprintf("%s:%s", workspace, timestamp)

	logrus.Infof("Creating snapshot image %s from container %s (original image: %s)", snapshotImageName, containerID, originalImage)

	// Commit the container
	commitOpts := dockercontainer.CommitOptions{
		Reference: snapshotImageName,
		Comment:   fmt.Sprintf("Snapshot created for workspace %s", workspace),
		Author:    "cri-dockerd-snapshot",
		Pause:     true,
	}

	logrus.Infof("Creating snapshot image %s from container %s (original image: %s)", snapshotImageName, containerID, originalImage)
	logrus.Infof("Commit options: Reference=%s, Comment=%s, Author=%s, Pause=%t",
		commitOpts.Reference, commitOpts.Comment, commitOpts.Author, commitOpts.Pause)

	// First, verify the container exists and is in a good state for commit
	containerInfo, inspectErr := ds.client.InspectContainer(containerID)
	if inspectErr != nil {
		logrus.Errorf("Cannot inspect container %s before commit: %v", containerID, inspectErr)
		return fmt.Errorf("failed to inspect container %s before commit: %v", containerID, inspectErr)
	}

	logrus.Infof("Container %s state before commit: Running=%t, Paused=%t, Status=%s",
		containerID, containerInfo.State.Running, containerInfo.State.Paused, containerInfo.State.Status)

	response, err := ds.client.CommitContainer(containerID, commitOpts)
	if err != nil {
		logrus.Errorf("Failed to commit container %s to image %s: %v", containerID, snapshotImageName, err)
		logrus.Errorf("Container state during commit failure: Running=%t, Paused=%t, Status=%s",
			containerInfo.State.Running, containerInfo.State.Paused, containerInfo.State.Status)
		return fmt.Errorf("failed to commit container %s: %v", containerID, err)
	}

	logrus.Infof("Successfully created snapshot image %s (ID: %s)", snapshotImageName, response.ID)

	// Verify the image was created by listing images
	images, listErr := ds.client.ListImages(dockerimagetypes.ListOptions{})
	if listErr == nil {
		found := false
		for _, img := range images {
			for _, tag := range img.RepoTags {
				if tag == snapshotImageName {
					found = true
					logrus.Infof("Verified snapshot image %s exists in Docker images list", snapshotImageName)
					break
				}
			}
			if found {
				break
			}
		}
		if !found {
			logrus.Warnf("Snapshot image %s not found in Docker images list after commit", snapshotImageName)
		}
	} else {
		logrus.Warnf("Failed to list images for verification: %v", listErr)
	}

	return nil
}

// selectImageForCreation determines which image to use for container creation
// Returns the snapshot image if available, otherwise returns the original image
func (ds *dockerService) selectImageForCreation(originalImage string, labels map[string]string) (string, error) {
	workspace := getWorkspaceFromLabels(labels)
	if workspace == "" {
		// No workspace specified, use original image
		return originalImage, nil
	}

	// Try to find the latest snapshot image
	snapshotImage, err := ds.findLatestSnapshotImage(originalImage, workspace)
	if err != nil {
		logrus.Warnf("Failed to find snapshot image for %s with workspace %s: %v", originalImage, workspace, err)
		return originalImage, nil
	}

	if snapshotImage == "" {
		// No snapshot found, use original image
		logrus.Infof("No snapshot image found for %s with workspace %s, using original image", originalImage, workspace)
		return originalImage, nil
	}

	// Use the snapshot image
	logrus.Infof("Using snapshot image %s instead of %s for workspace %s", snapshotImage, originalImage, workspace)
	return snapshotImage, nil
}
