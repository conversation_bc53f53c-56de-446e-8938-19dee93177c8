# CRI-Dockerd 快照功能二开文档

## 概述

本文档详细描述了在 cri-dockerd 中实现的容器快照功能的二次开发内容，包括代码结构、实现逻辑、集成方式和使用说明。

## 功能需求

### 核心需求
1. **容器删除时自动快照**: 当删除带有 `aocyun.snapshot=true` 标签的容器时，自动创建快照镜像
2. **容器创建时自动恢复**: 当创建容器时，如果指定了 `aocyun.workspace` 标签，自动使用最新的快照镜像

### 设计目标
- **简单性**: 使用简单的命名规则，避免复杂的镜像名解析
- **自动化**: 无需手动干预，自动完成快照的创建和使用
- **向后兼容**: 不影响现有功能，没有快照时正常使用原始镜像
- **容错性**: 快照操作失败不影响正常的容器生命周期

## 代码架构

### 文件结构
```
core/
├── snapshot_helper.go              # 快照功能核心实现
├── container_create.go             # 容器创建集成
├── container_remove.go             # 容器删除集成
├── snapshot_integration_test.go    # 集成测试
└── SNAPSHOT_FEATURE.md            # 功能说明文档
```

### 核心组件

#### 1. 常量定义 (snapshot_helper.go)
```go
const (
    SnapshotLabel      = "aocyun.snapshot"    // 快照启用标签
    WorkspaceLabel     = "aocyun.workspace"   // 工作空间标签
    SnapshotTimeFormat = "20060102150405"     // 时间戳格式
)
```

#### 2. 数据结构
```go
type SnapshotInfo struct {
    ImageName string // 镜像名称（工作空间名称）
    Workspace string // 工作空间名称
    Timestamp string // 快照创建时间戳
    FullTag   string // 完整的镜像标签
}
```

#### 3. 核心函数

##### 快照创建函数
- `createSnapshotImage()`: 提交容器创建快照镜像
- `handleContainerSnapshot()`: 容器删除时的快照处理协调器

##### 快照查找函数
- `findLatestSnapshotImageByWorkspace()`: 根据工作空间查找最新快照
- `selectImageForCreation()`: 容器创建时的镜像选择

##### 辅助函数
- `shouldCreateSnapshot()`: 检查是否应该创建快照
- `getWorkspaceFromLabels()`: 从标签中提取工作空间
- `parseImageName()`: 解析镜像名称和标签

## 实现逻辑

### 1. 快照创建流程

#### 触发条件
1. 容器删除请求 (`RemoveContainer`)
2. 容器或Pod标签包含 `aocyun.snapshot=true`
3. 标签包含有效的 `aocyun.workspace` 值

#### 执行步骤
```
1. RemoveContainer() 调用
   ↓
2. handleContainerSnapshot() 检查
   ↓
3. 检查容器和sandbox标签
   ↓
4. shouldCreateSnapshot() 判断
   ↓
5. getWorkspaceFromLabels() 提取工作空间
   ↓
6. createSnapshotImage() 创建快照
   ↓
7. Docker commit API 调用
   ↓
8. 验证快照镜像创建成功
```

#### 命名规则
- **格式**: `{workspace}:{timestamp}`
- **示例**: `jinxq-test-123123:20210507090909`
- **优势**: 简单、避免冲突、易于管理

### 2. 快照使用流程

#### 触发条件
1. 容器创建请求 (`CreateContainer`)
2. 容器或Pod标签包含 `aocyun.workspace` 值

#### 执行步骤
```
1. CreateContainer() 调用
   ↓
2. 合并容器和sandbox标签
   ↓
3. selectImageForCreation() 选择镜像
   ↓
4. getWorkspaceFromLabels() 提取工作空间
   ↓
5. findLatestSnapshotImageByWorkspace() 查找快照
   ↓
6. 按时间戳排序选择最新快照
   ↓
7. 返回快照镜像或原始镜像
   ↓
8. 使用选定镜像创建容器
```

## 集成方式

### 1. 容器删除集成 (container_remove.go)

```go
func (ds *dockerService) RemoveContainer(
    _ context.Context,
    r *v1.RemoveContainerRequest,
) (*v1.RemoveContainerResponse, error) {
    // ... 现有逻辑 ...
    
    // 快照功能集成点
    err = ds.handleContainerSnapshot(r.ContainerId)
    if err != nil {
        return nil, fmt.Errorf("failed to handle container snapshot for %q: %v", r.ContainerId, err)
    }
    
    // ... 继续删除逻辑 ...
}
```

### 2. 容器创建集成 (container_create.go)

```go
func (ds *dockerService) CreateContainer(
    _ context.Context,
    r *v1.CreateContainerRequest,
) (*v1.CreateContainerResponse, error) {
    // ... 现有逻辑 ...
    
    // 合并标签
    allLabels := make(map[string]string)
    // 添加容器标签和sandbox标签
    
    // 快照功能集成点
    selectedImage, err := ds.selectImageForCreation(image, allLabels)
    if err != nil {
        return nil, fmt.Errorf("failed to select image for container creation: %v", err)
    }
    image = selectedImage
    
    // ... 继续创建逻辑 ...
}
```

## 错误处理策略

### 1. 快照创建失败
- **策略**: 记录错误日志，但不阻塞容器删除
- **原因**: 避免快照功能影响正常的容器生命周期
- **实现**: 在 `handleContainerSnapshot` 中捕获错误

### 2. 快照查找失败
- **策略**: 回退到原始镜像
- **原因**: 确保容器创建不受快照功能影响
- **实现**: 在 `selectImageForCreation` 中提供回退机制

### 3. 配置错误
- **策略**: 记录警告日志，跳过快照操作
- **示例**: 有 `snapshot=true` 但没有 `workspace` 标签

## 测试策略

### 1. 单元测试
- 测试各个核心函数的独立功能
- 覆盖正常流程和异常情况
- 验证命名规则和时间戳格式

### 2. 集成测试
- 测试完整的快照创建和使用流程
- 验证容器删除和创建的集成
- 测试各种镜像格式的处理

### 3. 边界测试
- 测试digest格式镜像的处理
- 测试标签缺失的情况
- 测试Docker API失败的情况

## 部署和配置

### 1. 编译要求
- Go 1.19+
- Docker API 兼容性
- Linux 平台支持

### 2. 运行时要求
- Docker daemon 运行
- 足够的磁盘空间存储快照镜像
- Docker commit 权限

### 3. 配置说明
- 无需额外配置文件
- 通过Pod标签控制功能启用
- 支持动态启用/禁用

## 监控和日志

### 1. 关键日志
```
# 快照创建
INFO Creating snapshot image workspace:timestamp from container containerID
INFO Successfully created snapshot image workspace:timestamp (ID: imageID)

# 快照使用
INFO Found N snapshot images for workspace, using latest: workspace:timestamp
INFO Using snapshot image workspace:timestamp instead of original:tag for workspace
```

### 2. 错误日志
```
# 快照创建失败
ERROR Failed to commit container containerID to image workspace:timestamp: error

# 配置错误
WARN Container containerID has snapshot=true but no workspace label, skipping snapshot
```

## 性能考虑

### 1. 快照创建性能
- Docker commit 操作相对较慢
- 在容器删除流程中执行，不影响创建性能
- 可以考虑异步执行（未实现）

### 2. 快照查找性能
- 需要列出所有Docker镜像
- 在容器创建时执行，可能影响创建速度
- 可以考虑缓存优化（未实现）

### 3. 存储考虑
- 快照镜像占用额外磁盘空间
- 需要定期清理旧快照（未实现自动清理）
- 建议监控磁盘使用情况

## 未来改进方向

### 1. 功能增强
- 快照镜像自动清理
- 快照压缩和去重
- 跨节点快照同步

### 2. 性能优化
- 快照查找缓存
- 异步快照创建
- 增量快照支持

### 3. 运维增强
- 快照管理API
- 监控指标暴露
- 配置文件支持

## 总结

本快照功能的二开实现具有以下特点：

1. **最小侵入**: 仅在必要的集成点添加代码，不影响现有功能
2. **简单可靠**: 使用简单的命名规则和清晰的逻辑流程
3. **容错设计**: 提供完善的错误处理和回退机制
4. **易于维护**: 代码结构清晰，注释详细，测试覆盖完整

该实现满足了原始需求，为容器的快照管理提供了自动化的解决方案。
