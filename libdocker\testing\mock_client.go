// Code generated by MockGen. DO NOT EDIT.
// Source: libdocker/client.go

// Package testing is a generated GoMock package.
package testing

import (
	reflect "reflect"
	time "time"

	libdocker "github.com/Mirantis/cri-dockerd/libdocker"
	types "github.com/docker/docker/api/types"
	backend "github.com/docker/docker/api/types/backend"
	container "github.com/docker/docker/api/types/container"
	image "github.com/docker/docker/api/types/image"
	registry "github.com/docker/docker/api/types/registry"
	system "github.com/docker/docker/api/types/system"
	gomock "github.com/golang/mock/gomock"
)

// MockDockerClientInterface is a mock of DockerClientInterface interface.
type MockDockerClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDockerClientInterfaceMockRecorder
}

// MockDockerClientInterfaceMockRecorder is the mock recorder for MockDockerClientInterface.
type MockDockerClientInterfaceMockRecorder struct {
	mock *MockDockerClientInterface
}

// NewMockDockerClientInterface creates a new mock instance.
func NewMockDockerClientInterface(ctrl *gomock.Controller) *MockDockerClientInterface {
	mock := &MockDockerClientInterface{ctrl: ctrl}
	mock.recorder = &MockDockerClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDockerClientInterface) EXPECT() *MockDockerClientInterfaceMockRecorder {
	return m.recorder
}

// AttachToContainer mocks base method.
func (m *MockDockerClientInterface) AttachToContainer(arg0 string, arg1 container.AttachOptions, arg2 libdocker.StreamOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachToContainer", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachToContainer indicates an expected call of AttachToContainer.
func (mr *MockDockerClientInterfaceMockRecorder) AttachToContainer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachToContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).AttachToContainer), arg0, arg1, arg2)
}

// CommitContainer mocks base method.
func (m *MockDockerClientInterface) CommitContainer(containerID string, opts container.CommitOptions) (*types.IDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitContainer", containerID, opts)
	ret0, _ := ret[0].(*types.IDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommitContainer indicates an expected call of CommitContainer.
func (mr *MockDockerClientInterfaceMockRecorder) CommitContainer(containerID, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).CommitContainer), containerID, opts)
}

// CreateContainer mocks base method.
func (m *MockDockerClientInterface) CreateContainer(arg0 backend.ContainerCreateConfig) (*container.CreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContainer", arg0)
	ret0, _ := ret[0].(*container.CreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContainer indicates an expected call of CreateContainer.
func (mr *MockDockerClientInterfaceMockRecorder) CreateContainer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).CreateContainer), arg0)
}

// CreateExec mocks base method.
func (m *MockDockerClientInterface) CreateExec(arg0 string, arg1 container.ExecOptions) (*types.IDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExec", arg0, arg1)
	ret0, _ := ret[0].(*types.IDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExec indicates an expected call of CreateExec.
func (mr *MockDockerClientInterfaceMockRecorder) CreateExec(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExec", reflect.TypeOf((*MockDockerClientInterface)(nil).CreateExec), arg0, arg1)
}

// GetContainerStats mocks base method.
func (m *MockDockerClientInterface) GetContainerStats(id string) (*container.StatsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContainerStats", id)
	ret0, _ := ret[0].(*container.StatsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContainerStats indicates an expected call of GetContainerStats.
func (mr *MockDockerClientInterfaceMockRecorder) GetContainerStats(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContainerStats", reflect.TypeOf((*MockDockerClientInterface)(nil).GetContainerStats), id)
}

// ImageHistory mocks base method.
func (m *MockDockerClientInterface) ImageHistory(id string) ([]image.HistoryResponseItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageHistory", id)
	ret0, _ := ret[0].([]image.HistoryResponseItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageHistory indicates an expected call of ImageHistory.
func (mr *MockDockerClientInterfaceMockRecorder) ImageHistory(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageHistory", reflect.TypeOf((*MockDockerClientInterface)(nil).ImageHistory), id)
}

// Info mocks base method.
func (m *MockDockerClientInterface) Info() (*system.Info, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Info")
	ret0, _ := ret[0].(*system.Info)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Info indicates an expected call of Info.
func (mr *MockDockerClientInterfaceMockRecorder) Info() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Info", reflect.TypeOf((*MockDockerClientInterface)(nil).Info))
}

// InspectContainer mocks base method.
func (m *MockDockerClientInterface) InspectContainer(id string) (*types.ContainerJSON, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InspectContainer", id)
	ret0, _ := ret[0].(*types.ContainerJSON)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InspectContainer indicates an expected call of InspectContainer.
func (mr *MockDockerClientInterfaceMockRecorder) InspectContainer(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InspectContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).InspectContainer), id)
}

// InspectContainerWithSize mocks base method.
func (m *MockDockerClientInterface) InspectContainerWithSize(id string) (*types.ContainerJSON, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InspectContainerWithSize", id)
	ret0, _ := ret[0].(*types.ContainerJSON)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InspectContainerWithSize indicates an expected call of InspectContainerWithSize.
func (mr *MockDockerClientInterfaceMockRecorder) InspectContainerWithSize(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InspectContainerWithSize", reflect.TypeOf((*MockDockerClientInterface)(nil).InspectContainerWithSize), id)
}

// InspectExec mocks base method.
func (m *MockDockerClientInterface) InspectExec(id string) (*container.ExecInspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InspectExec", id)
	ret0, _ := ret[0].(*container.ExecInspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InspectExec indicates an expected call of InspectExec.
func (mr *MockDockerClientInterfaceMockRecorder) InspectExec(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InspectExec", reflect.TypeOf((*MockDockerClientInterface)(nil).InspectExec), id)
}

// InspectImageByID mocks base method.
func (m *MockDockerClientInterface) InspectImageByID(imageID string) (*types.ImageInspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InspectImageByID", imageID)
	ret0, _ := ret[0].(*types.ImageInspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InspectImageByID indicates an expected call of InspectImageByID.
func (mr *MockDockerClientInterfaceMockRecorder) InspectImageByID(imageID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InspectImageByID", reflect.TypeOf((*MockDockerClientInterface)(nil).InspectImageByID), imageID)
}

// InspectImageByRef mocks base method.
func (m *MockDockerClientInterface) InspectImageByRef(imageRef string) (*types.ImageInspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InspectImageByRef", imageRef)
	ret0, _ := ret[0].(*types.ImageInspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InspectImageByRef indicates an expected call of InspectImageByRef.
func (mr *MockDockerClientInterfaceMockRecorder) InspectImageByRef(imageRef interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InspectImageByRef", reflect.TypeOf((*MockDockerClientInterface)(nil).InspectImageByRef), imageRef)
}

// ListContainers mocks base method.
func (m *MockDockerClientInterface) ListContainers(options container.ListOptions) ([]types.Container, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListContainers", options)
	ret0, _ := ret[0].([]types.Container)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListContainers indicates an expected call of ListContainers.
func (mr *MockDockerClientInterfaceMockRecorder) ListContainers(options interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListContainers", reflect.TypeOf((*MockDockerClientInterface)(nil).ListContainers), options)
}

// ListImages mocks base method.
func (m *MockDockerClientInterface) ListImages(opts image.ListOptions) ([]image.Summary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListImages", opts)
	ret0, _ := ret[0].([]image.Summary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListImages indicates an expected call of ListImages.
func (mr *MockDockerClientInterfaceMockRecorder) ListImages(opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListImages", reflect.TypeOf((*MockDockerClientInterface)(nil).ListImages), opts)
}

// Logs mocks base method.
func (m *MockDockerClientInterface) Logs(arg0 string, arg1 container.LogsOptions, arg2 libdocker.StreamOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Logs", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Logs indicates an expected call of Logs.
func (mr *MockDockerClientInterfaceMockRecorder) Logs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logs", reflect.TypeOf((*MockDockerClientInterface)(nil).Logs), arg0, arg1, arg2)
}

// PullImage mocks base method.
func (m *MockDockerClientInterface) PullImage(image string, auth registry.AuthConfig, opts image.PullOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PullImage", image, auth, opts)
	ret0, _ := ret[0].(error)
	return ret0
}

// PullImage indicates an expected call of PullImage.
func (mr *MockDockerClientInterfaceMockRecorder) PullImage(image, auth, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullImage", reflect.TypeOf((*MockDockerClientInterface)(nil).PullImage), image, auth, opts)
}

// RemoveContainer mocks base method.
func (m *MockDockerClientInterface) RemoveContainer(id string, opts container.RemoveOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveContainer", id, opts)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveContainer indicates an expected call of RemoveContainer.
func (mr *MockDockerClientInterfaceMockRecorder) RemoveContainer(id, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).RemoveContainer), id, opts)
}

// RemoveImage mocks base method.
func (m *MockDockerClientInterface) RemoveImage(imageStr string, opts image.RemoveOptions) ([]image.DeleteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveImage", imageStr, opts)
	ret0, _ := ret[0].([]image.DeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveImage indicates an expected call of RemoveImage.
func (mr *MockDockerClientInterfaceMockRecorder) RemoveImage(imageStr, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveImage", reflect.TypeOf((*MockDockerClientInterface)(nil).RemoveImage), imageStr, opts)
}

// ResizeContainerTTY mocks base method.
func (m *MockDockerClientInterface) ResizeContainerTTY(id string, height, width uint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResizeContainerTTY", id, height, width)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResizeContainerTTY indicates an expected call of ResizeContainerTTY.
func (mr *MockDockerClientInterfaceMockRecorder) ResizeContainerTTY(id, height, width interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeContainerTTY", reflect.TypeOf((*MockDockerClientInterface)(nil).ResizeContainerTTY), id, height, width)
}

// ResizeExecTTY mocks base method.
func (m *MockDockerClientInterface) ResizeExecTTY(id string, height, width uint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResizeExecTTY", id, height, width)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResizeExecTTY indicates an expected call of ResizeExecTTY.
func (mr *MockDockerClientInterfaceMockRecorder) ResizeExecTTY(id, height, width interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeExecTTY", reflect.TypeOf((*MockDockerClientInterface)(nil).ResizeExecTTY), id, height, width)
}

// StartContainer mocks base method.
func (m *MockDockerClientInterface) StartContainer(id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartContainer", id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartContainer indicates an expected call of StartContainer.
func (mr *MockDockerClientInterfaceMockRecorder) StartContainer(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).StartContainer), id)
}

// StartExec mocks base method.
func (m *MockDockerClientInterface) StartExec(arg0 string, arg1 container.ExecStartOptions, arg2 libdocker.StreamOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartExec", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartExec indicates an expected call of StartExec.
func (mr *MockDockerClientInterfaceMockRecorder) StartExec(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartExec", reflect.TypeOf((*MockDockerClientInterface)(nil).StartExec), arg0, arg1, arg2)
}

// StopContainer mocks base method.
func (m *MockDockerClientInterface) StopContainer(id string, timeout time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopContainer", id, timeout)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopContainer indicates an expected call of StopContainer.
func (mr *MockDockerClientInterfaceMockRecorder) StopContainer(id, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopContainer", reflect.TypeOf((*MockDockerClientInterface)(nil).StopContainer), id, timeout)
}

// UpdateContainerResources mocks base method.
func (m *MockDockerClientInterface) UpdateContainerResources(id string, updateConfig container.UpdateConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateContainerResources", id, updateConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateContainerResources indicates an expected call of UpdateContainerResources.
func (mr *MockDockerClientInterfaceMockRecorder) UpdateContainerResources(id, updateConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateContainerResources", reflect.TypeOf((*MockDockerClientInterface)(nil).UpdateContainerResources), id, updateConfig)
}

// Version mocks base method.
func (m *MockDockerClientInterface) Version() (*types.Version, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Version")
	ret0, _ := ret[0].(*types.Version)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Version indicates an expected call of Version.
func (mr *MockDockerClientInterfaceMockRecorder) Version() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Version", reflect.TypeOf((*MockDockerClientInterface)(nil).Version))
}
