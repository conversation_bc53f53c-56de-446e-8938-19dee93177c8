/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package core

import (
	"testing"
	"time"

	"github.com/Mirantis/cri-dockerd/libdocker"
	dockerimagetypes "github.com/docker/docker/api/types/image"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseImageName(t *testing.T) {
	tests := []struct {
		input        string
		expectedName string
		expectedTag  string
	}{
		{"nginx", "nginx", "latest"},
		{"nginx:v1", "nginx", "v1"},
		{"registry.com/nginx:v1", "registry.com/nginx", "v1"},
		{"registry.com:5000/nginx:v1", "registry.com:5000/nginx", "v1"},
		// Test digest format
		{"nginx@sha256:abc123", "nginx", "latest"},
		{"registry.com/nginx@sha256:def456", "registry.com/nginx", "latest"},
		{"registry.com:5000/nginx@sha256:ghi789", "registry.com:5000/nginx", "latest"},
	}

	for _, test := range tests {
		name, tag := parseImageName(test.input)
		assert.Equal(t, test.expectedName, name, "Image name mismatch for %s", test.input)
		assert.Equal(t, test.expectedTag, tag, "Tag mismatch for %s", test.input)
	}
}

func TestGenerateSnapshotTag(t *testing.T) {
	workspace := "jinxq-test-123123"
	tag := generateSnapshotTag(workspace)

	// Should contain workspace
	assert.Contains(t, tag, workspace)

	// Should contain underscore
	assert.Contains(t, tag, "_")

	// Should end with timestamp format
	parts := splitSnapshotTag(tag)
	assert.Len(t, parts, 2)
	assert.Equal(t, workspace, parts[0])

	// Timestamp should be parseable
	_, err := time.Parse(SnapshotTimeFormat, parts[1])
	assert.NoError(t, err)
}

func splitSnapshotTag(tag string) []string {
	// Helper function to split tag for testing
	parts := make([]string, 0)
	lastUnderscore := -1
	for i := len(tag) - 1; i >= 0; i-- {
		if tag[i] == '_' {
			lastUnderscore = i
			break
		}
	}
	if lastUnderscore > 0 {
		parts = append(parts, tag[:lastUnderscore])
		parts = append(parts, tag[lastUnderscore+1:])
	}
	return parts
}

func TestParseSnapshotTag(t *testing.T) {
	tests := []struct {
		tag                string
		expectedWorkspace  string
		expectedTimestamp  string
		expectedIsSnapshot bool
	}{
		{"jinxq-test-123123_20210507090909", "jinxq-test-123123", "20210507090909", true},
		{"workspace_20210507090909", "workspace", "20210507090909", true},
		{"complex-workspace-name_20210507090909", "complex-workspace-name", "20210507090909", true},
		{"v1", "", "", false},
		{"latest", "", "", false},
		{"workspace_invalid", "", "", false},
		{"workspace_2021050709090", "", "", false}, // Wrong timestamp length
	}

	for _, test := range tests {
		workspace, timestamp, isSnapshot := parseSnapshotTag(test.tag)
		assert.Equal(t, test.expectedWorkspace, workspace, "Workspace mismatch for %s", test.tag)
		assert.Equal(t, test.expectedTimestamp, timestamp, "Timestamp mismatch for %s", test.tag)
		assert.Equal(t, test.expectedIsSnapshot, isSnapshot, "IsSnapshot mismatch for %s", test.tag)
	}
}

func TestShouldCreateSnapshot(t *testing.T) {
	tests := []struct {
		labels   map[string]string
		expected bool
	}{
		{nil, false},
		{map[string]string{}, false},
		{map[string]string{SnapshotLabel: "false"}, false},
		{map[string]string{SnapshotLabel: "true"}, true},
		{map[string]string{SnapshotLabel: "True"}, false}, // Case sensitive
		{map[string]string{"other": "true"}, false},
	}

	for _, test := range tests {
		result := shouldCreateSnapshot(test.labels)
		assert.Equal(t, test.expected, result, "Unexpected result for labels %v", test.labels)
	}
}

func TestGetWorkspaceFromLabels(t *testing.T) {
	tests := []struct {
		labels   map[string]string
		expected string
	}{
		{nil, ""},
		{map[string]string{}, ""},
		{map[string]string{WorkspaceLabel: "jinxq-test-123123"}, "jinxq-test-123123"},
		{map[string]string{"other": "value"}, ""},
	}

	for _, test := range tests {
		result := getWorkspaceFromLabels(test.labels)
		assert.Equal(t, test.expected, result, "Unexpected result for labels %v", test.labels)
	}
}

func TestFindLatestSnapshotImage(t *testing.T) {
	// Create a fake docker service
	fakeClient := libdocker.NewFakeDockerClient()
	ds := &dockerService{
		client: fakeClient,
	}

	// Inject some test images
	testImages := []dockerimagetypes.Summary{
		{
			ID:       "image1",
			RepoTags: []string{"nginx:latest"},
		},
		{
			ID:       "image2",
			RepoTags: []string{"nginx:jinxq-test-123123_20210507090909"},
		},
		{
			ID:       "image3",
			RepoTags: []string{"nginx:jinxq-test-123123_20210507091010"}, // Newer
		},
		{
			ID:       "image4",
			RepoTags: []string{"nginx:other-workspace_20210507090909"},
		},
	}
	fakeClient.InjectImages(testImages)

	// Test finding latest snapshot
	result, err := ds.findLatestSnapshotImage("nginx:latest", "jinxq-test-123123")
	require.NoError(t, err)
	assert.Equal(t, "nginx:jinxq-test-123123_20210507091010", result)

	// Test with non-existent workspace
	result, err = ds.findLatestSnapshotImage("nginx:latest", "non-existent")
	require.NoError(t, err)
	assert.Equal(t, "", result)

	// Test with empty workspace
	result, err = ds.findLatestSnapshotImage("nginx:latest", "")
	require.NoError(t, err)
	assert.Equal(t, "", result)
}

func TestSelectImageForCreation(t *testing.T) {
	// Create a fake docker service
	fakeClient := libdocker.NewFakeDockerClient()
	ds := &dockerService{
		client: fakeClient,
	}

	// Inject some test images
	testImages := []dockerimagetypes.Summary{
		{
			ID:       "image1",
			RepoTags: []string{"nginx:latest"},
		},
		{
			ID:       "image2",
			RepoTags: []string{"nginx:jinxq-test-123123_20210507090909"},
		},
	}
	fakeClient.InjectImages(testImages)

	// Test with workspace that has snapshot
	labels := map[string]string{WorkspaceLabel: "jinxq-test-123123"}
	result, err := ds.selectImageForCreation("nginx:latest", labels)
	require.NoError(t, err)
	assert.Equal(t, "nginx:jinxq-test-123123_20210507090909", result)

	// Test with workspace that has no snapshot
	labels = map[string]string{WorkspaceLabel: "no-snapshot"}
	result, err = ds.selectImageForCreation("nginx:latest", labels)
	require.NoError(t, err)
	assert.Equal(t, "nginx:latest", result)

	// Test with no workspace
	result, err = ds.selectImageForCreation("nginx:latest", nil)
	require.NoError(t, err)
	assert.Equal(t, "nginx:latest", result)
}
