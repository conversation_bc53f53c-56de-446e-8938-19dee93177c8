/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package core

import (
	"context"
	"testing"

	"github.com/Mirantis/cri-dockerd/libdocker"
	dockercontainer "github.com/docker/docker/api/types/container"
	dockerimagetypes "github.com/docker/docker/api/types/image"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/cri-api/pkg/apis/runtime/v1"
)

func TestSnapshotIntegration(t *testing.T) {
	// Create a fake docker service
	fakeClient := libdocker.NewFakeDockerClient()
	ds := &dockerService{
		client: fakeClient,
	}

	// Test scenario: Create container with snapshot labels, then remove it
	t.Run("CreateAndRemoveContainerWithSnapshot", func(t *testing.T) {
		// Setup: Inject a base image
		testImages := []dockerimagetypes.Summary{
			{
				ID:       "nginx-base",
				RepoTags: []string{"nginx:v1"},
			},
		}
		fakeClient.InjectImages(testImages)

		// Setup: Create a fake container
		containerID := "test-container-123"
		fakeClient.SetFakeContainers([]*libdocker.FakeContainer{
			{
				ID:   containerID,
				Name: "test-container",
				Config: &dockercontainer.Config{
					Image: "nginx:v1",
					Labels: map[string]string{
						SnapshotLabel:  "true",
						WorkspaceLabel: "jinxq-test-123123",
					},
				},
			},
		})

		// Test: Remove container (should create snapshot)
		removeReq := &v1.RemoveContainerRequest{
			ContainerId: containerID,
		}

		_, err := ds.RemoveContainer(context.Background(), removeReq)
		require.NoError(t, err)

		// Verify: The test passes if no error occurred during removal
		// In a real scenario, we would check if the snapshot image was created
		// For now, we just verify that the removal succeeded without errors
	})

	t.Run("CreateContainerWithExistingSnapshot", func(t *testing.T) {
		// Setup: Inject images including a snapshot
		testImages := []dockerimagetypes.Summary{
			{
				ID:       "nginx-base",
				RepoTags: []string{"nginx:v1"},
			},
			{
				ID:       "nginx-snapshot",
				RepoTags: []string{"nginx:jinxq-test-123123_20210507090909"},
			},
		}
		fakeClient.InjectImages(testImages)

		// Test: Select image for creation
		labels := map[string]string{
			WorkspaceLabel: "jinxq-test-123123",
		}

		selectedImage, err := ds.selectImageForCreation("nginx:v1", labels)
		require.NoError(t, err)

		// Verify: Should select the snapshot image
		assert.Equal(t, "nginx:jinxq-test-123123_20210507090909", selectedImage)
	})

	t.Run("CreateContainerWithoutSnapshot", func(t *testing.T) {
		// Setup: Inject only base image
		testImages := []dockerimagetypes.Summary{
			{
				ID:       "nginx-base",
				RepoTags: []string{"nginx:v1"},
			},
		}
		fakeClient.InjectImages(testImages)

		// Test: Select image for creation with workspace that has no snapshot
		labels := map[string]string{
			WorkspaceLabel: "no-snapshot-workspace",
		}

		selectedImage, err := ds.selectImageForCreation("nginx:v1", labels)
		require.NoError(t, err)

		// Verify: Should use original image
		assert.Equal(t, "nginx:v1", selectedImage)
	})

	t.Run("CreateContainerWithoutWorkspace", func(t *testing.T) {
		// Test: Select image for creation without workspace
		selectedImage, err := ds.selectImageForCreation("nginx:v1", nil)
		require.NoError(t, err)

		// Verify: Should use original image
		assert.Equal(t, "nginx:v1", selectedImage)
	})

	t.Run("RemoveContainerWithoutSnapshotLabel", func(t *testing.T) {
		// Setup: Create a fake container without snapshot label
		containerID := "test-container-no-snapshot"
		fakeClient.SetFakeContainers([]*libdocker.FakeContainer{
			{
				ID:   containerID,
				Name: "test-container-no-snapshot",
				Config: &dockercontainer.Config{
					Image: "nginx:v1",
					Labels: map[string]string{
						WorkspaceLabel: "jinxq-test-123123",
						// No snapshot label
					},
				},
			},
		})

		// Test: Remove container (should not create snapshot)
		removeReq := &v1.RemoveContainerRequest{
			ContainerId: containerID,
		}

		_, err := ds.RemoveContainer(context.Background(), removeReq)
		require.NoError(t, err)

		// Verify: The test passes if no error occurred during removal
	})

	t.Run("RemoveContainerWithSnapshotButNoWorkspace", func(t *testing.T) {
		// Setup: Create a fake container with snapshot label but no workspace
		containerID := "test-container-no-workspace"
		fakeClient.SetFakeContainers([]*libdocker.FakeContainer{
			{
				ID:   containerID,
				Name: "test-container-no-workspace",
				Config: &dockercontainer.Config{
					Image: "nginx:v1",
					Labels: map[string]string{
						SnapshotLabel: "true",
						// No workspace label
					},
				},
			},
		})

		// Test: Remove container (should not create snapshot due to missing workspace)
		removeReq := &v1.RemoveContainerRequest{
			ContainerId: containerID,
		}

		_, err := ds.RemoveContainer(context.Background(), removeReq)
		require.NoError(t, err)

		// Verify: The test passes if no error occurred during removal
	})
}
