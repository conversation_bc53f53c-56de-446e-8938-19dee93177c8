/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package core

import (
	"context"
	"fmt"
	"time"

	"github.com/Mirantis/cri-dockerd/libdocker"
	"github.com/docker/docker/api/types"
	dockercontainer "github.com/docker/docker/api/types/container"
	"github.com/sirupsen/logrus"
	v1 "k8s.io/cri-api/pkg/apis/runtime/v1"
)

// RemoveContainer removes the container.
func (ds *dockerService) RemoveContainer(
	_ context.Context,
	r *v1.RemoveContainerRequest,
) (*v1.RemoveContainerResponse, error) {
	// Ideally, log lifecycle should be independent of container lifecycle.
	// However, docker will remove container log after container is removed,
	// we can't prevent that now, so we also clean up the symlink here.
	err := ds.removeContainerLogSymlink(r.ContainerId)
	if err != nil {
		return nil, err
	}
	// Check if we need to create a snapshot before removing the container
	err = ds.handleContainerSnapshot(r.ContainerId)
	if err != nil {
		return nil, fmt.Errorf("failed to handle container snapshot for %q: %v", r.ContainerId, err)
	}

	errors := ds.performPlatformSpecificContainerForContainer(r.ContainerId)
	if len(errors) != 0 {
		return nil, fmt.Errorf(
			"failed to run platform-specific clean ups for container %q: %v",
			r.ContainerId,
			errors,
		)
	}
	err = ds.client.RemoveContainer(
		r.ContainerId,
		dockercontainer.RemoveOptions{RemoveVolumes: true, Force: true},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to remove container %q: %v", r.ContainerId, err)
	}

	return &v1.RemoveContainerResponse{}, nil
}

func (ds *dockerService) getContainerCleanupInfo(containerID string) (*containerCleanupInfo, bool) {
	ds.cleanupInfosLock.RLock()
	defer ds.cleanupInfosLock.RUnlock()
	info, ok := ds.containerCleanupInfos[containerID]
	return info, ok
}

func (ds *dockerService) setContainerCleanupInfo(containerID string, info *containerCleanupInfo) {
	ds.cleanupInfosLock.Lock()
	defer ds.cleanupInfosLock.Unlock()
	ds.containerCleanupInfos[containerID] = info
}

func (ds *dockerService) clearContainerCleanupInfo(containerID string) {
	ds.cleanupInfosLock.Lock()
	defer ds.cleanupInfosLock.Unlock()
	delete(ds.containerCleanupInfos, containerID)
}

func getContainerTimestamps(r *types.ContainerJSON) (time.Time, time.Time, time.Time, error) {
	var createdAt, startedAt, finishedAt time.Time
	var err error

	createdAt, err = libdocker.ParseDockerTimestamp(r.Created)
	if err != nil {
		return createdAt, startedAt, finishedAt, err
	}
	startedAt, err = libdocker.ParseDockerTimestamp(r.State.StartedAt)
	if err != nil {
		return createdAt, startedAt, finishedAt, err
	}
	finishedAt, err = libdocker.ParseDockerTimestamp(r.State.FinishedAt)
	if err != nil {
		return createdAt, startedAt, finishedAt, err
	}
	return createdAt, startedAt, finishedAt, nil
}

// handleContainerSnapshot checks if a container should be snapshotted before removal
// and creates the snapshot if needed
func (ds *dockerService) handleContainerSnapshot(containerID string) error {
	// Inspect the container to get its labels and image information
	containerInfo, err := ds.client.InspectContainer(containerID)
	if err != nil {
		// If container doesn't exist, we can't snapshot it, but that's not an error for removal
		if libdocker.IsContainerNotFoundError(err) {
			return nil
		}
		return fmt.Errorf("failed to inspect container %s: %v", containerID, err)
	}

	// Get all labels from both container and sandbox
	allLabels := make(map[string]string)

	// Add container labels
	if containerInfo.Config.Labels != nil {
		for k, v := range containerInfo.Config.Labels {
			allLabels[k] = v
		}
	}

	// Get sandbox ID and inspect sandbox for Pod labels
	sandboxID := containerInfo.Config.Labels[sandboxIDLabelKey]
	if sandboxID != "" {
		sandboxInfo, err := ds.client.InspectContainer(sandboxID)
		if err == nil && sandboxInfo.Config.Labels != nil {
			// Add sandbox labels (Pod labels are typically here)
			for k, v := range sandboxInfo.Config.Labels {
				allLabels[k] = v
			}
		} else {
			logrus.Warnf("Failed to inspect sandbox %s for container %s: %v", sandboxID, containerID, err)
		}
	}

	// Check if snapshot is required
	if !shouldCreateSnapshot(allLabels) {
		return nil
	}

	// Get workspace from labels
	workspace := getWorkspaceFromLabels(allLabels)
	if workspace == "" {
		logrus.Warnf("Container %s has snapshot=true but no workspace label, skipping snapshot", containerID)
		return nil
	}

	// Get the original image name
	originalImage := containerInfo.Config.Image
	if originalImage == "" {
		return fmt.Errorf("container %s has no image information", containerID)
	}

	// Create the snapshot
	return ds.createSnapshotImage(containerID, originalImage, workspace)
}
